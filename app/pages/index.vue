<script setup lang="ts">
const { signOut, data } = useAuth();

const userName = computed(() => data.value?.user?.name);
</script>

<template>
    <div class="flex min-h-screen items-center justify-center relative">
        <div class="absolute top-4 right-4 flex items-center gap-4">
            <p>Welcome, {{ userName }}!</p>
            <UButton
                color="neutral"
                variant="outline"
                size="md"
                class="cursor-pointer"
                @click="() => signOut()"
            >
                Sign Out
            </UButton>
            <ThemeToggle />
        </div>

        <div class="text-center">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                SenseHawk Console
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
                This is the SenseHawk Console. You're logged in.
            </p>
        </div>
    </div>
</template>
